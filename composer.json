{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "bacon/bacon-qr-code": "^2.0", "beyondcode/laravel-websockets": "^1.14", "doctrine/dbal": "^3.6", "egulias/email-validator": "^4.0", "ezyang/htmlpurifier": "^4.17", "google/recaptcha": "^1.3", "guzzlehttp/guzzle": "^7.2", "guzzlehttp/psr7": "^2.6", "ifsnop/mysqldump-php": "^2.12", "illuminate/contracts": "*", "intervention/image": "^2.7", "jenssegers/agent": "^2.6", "laravel-lara/lskllc": "^1.0", "laravel/framework": "^10.43", "laravel/helpers": "^1.6", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.6", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "league/flysystem-aws-s3-v3": "^3.24", "maatwebsite/excel": "^3.1", "mews/captcha": "^3.3", "pear/text_languagedetect": "^1.0", "pragmarx/google2fa-laravel": "^2.1", "propaganistas/laravel-disposable-email": "^2.2", "pusher/pusher-php-server": "^7.2", "rachidlaasri/laravel-installer": "^4.1", "ratchet/pawl": "^0.4.1", "react/socket": "^1.16", "socialiteproviders/envato": "^4.1", "socialiteproviders/microsoft": "^4.2", "socialiteproviders/microsoft-graph": "^4.1", "spatie/laravel-cookie-consent": "^3.2", "spatie/laravel-honeypot": "^4.3", "spatie/laravel-medialibrary": "^10.9", "spatie/laravel-permission": "^5.10", "spatie/ssl-certificate": "^2.6", "stichoza/google-translate-php": "^5.2", "swiftmailer/swiftmailer": "^5.4", "symfony/finder": "^6.4", "symfony/mime": "^6.4", "symfony/polyfill-iconv": "^1.29", "torann/geoip": "^3.0", "twilio/sdk": "^8.3", "webklex/laravel-imap": "^5.2"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Livachat\\Addons\\App\\": "addons/src/app/", "Livachat\\Addons\\": "addons/src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["app/helpers.php", "app/Helper/languagehelper.php"]}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}