<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Network Interceptor Middleware
 * 
 * This middleware intercepts and blocks external network calls from the malicious package
 * while allowing all internal application functionality to work normally.
 */
class NetworkInterceptor
{
    /**
     * List of suspicious domains/IPs that should be blocked
     */
    private $blockedDomains = [
        'spruko.com',
        'panel.spruko.com',
        'api.spruko.com',
        // Add more suspicious domains as discovered
    ];

    /**
     * List of suspicious URL patterns that should be blocked
     */
    private $blockedPatterns = [
        '/api\/api\/apidetail/',
        '/malware/',
        '/backdoor/',
        '/exploit/',
        // Add more patterns as discovered
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if this is an outgoing HTTP request from the malicious package
        if ($this->isSuspiciousRequest($request)) {
            Log::warning('NetworkInterceptor: Blocked suspicious external request', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip(),
                'headers' => $request->headers->all()
            ]);

            // Return a mock successful response
            return response()->json([
                'status' => 'success',
                'message' => 'Request processed successfully',
                'data' => [
                    'intercepted' => true,
                    'timestamp' => now()->toISOString()
                ]
            ], 200);
        }

        return $next($request);
    }

    /**
     * Check if the request is suspicious and should be blocked
     */
    private function isSuspiciousRequest(Request $request): bool
    {
        $url = $request->fullUrl();
        $host = parse_url($url, PHP_URL_HOST);

        // Check blocked domains
        foreach ($this->blockedDomains as $domain) {
            if (str_contains($host, $domain)) {
                return true;
            }
        }

        // Check blocked URL patterns
        foreach ($this->blockedPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        // Check for suspicious user agents or headers that might indicate malware
        $userAgent = $request->userAgent();
        if ($userAgent && (
            str_contains($userAgent, 'malware') ||
            str_contains($userAgent, 'bot') ||
            str_contains($userAgent, 'crawler')
        )) {
            return true;
        }

        return false;
    }
}
