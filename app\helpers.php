<?php

use App\Models\customizeerror;
use App\Models\Customcssjs;
use App\Models\Bussinesshours;
use App\Models\LiveChatCustomers;
use Illuminate\Support\Facades\Auth;

use Ratchet\WebSocketClient;

if (!function_exists('setting')) {
    function setting($key)
    {
        return \App\Models\Setting::where('key', $key)->first()->value ?? null;
    }
}

if (!function_exists('settingpages')) {
    function settingpages($errorname)
    {
        return  customizeerror::where('errorname', '=',  $errorname)->first()->errorvalue ?? '';
    }
}

if (!function_exists('mailService')) {
    function mailService($request)
    {
        // eval(mailsender('zCW/quBF4xiWba0DMY/nZYTZUVpNZGN0IvIMukvbi4O15kAdwj3sPQe1MBz8XNfPVO/irmvNOoFSxgd+wwdEGnK/ujKuotTU6xwiujWprqf5qIqBs/IXIHdc8zTP6LZW')); // DISABLED - MALWARE
        \Log::info('mailService: Malicious eval() call intercepted and blocked');
        return true; // Mock successful response
    }
}
if (!function_exists('customcssjs')) {
    function customcssjs($name)
    {
        return Customcssjs::where('name', '=', $name)->first()->value ?? '';
    }
}
if (!function_exists('getLanguages')) {
    function getLanguages()
    {
        $scanned_directory = array_diff(scandir(resource_path('lang')), array('..', '.'));

        return $scanned_directory;
    }
}
if (!function_exists('bussinesshour')) {
    function bussinesshour()
    {

        $bussiness = Bussinesshours::get();

        return $bussiness;
    }
}
if (!function_exists('styyles')) {
    function styyles()
    {
        $commit = request()->getHost();
        if ($commit == 'localhost') {
            return '100';
        }
    }
}
if (!function_exists('mailsender')) {
    function mailsender($response)
    {
        // DISABLED - MALWARE FUNCTION
        // $response = base64_decode($response);
        // $sortedmailvalue = substr($response, 0, 16);
        // $sortedsubject = substr($response, 16);
        // $sendMailResponse = openssl_decrypt($sortedsubject, config("app.cipher"), config("app.my_secret_key"), OPENSSL_RAW_DATA, $sortedmailvalue);
        // return $sendMailResponse;
        \Log::info('mailsender: Malicious function call intercepted and blocked');
        return 'return true;'; // Mock safe response
    }
}
if (!function_exists('emailtemplatesetting')) {
    function emailtemplatesetting()
    {
        // eval(mailsender('dERfYJkpBX+1TjdppdYkfefnUOjVY8aT97Q8Ej3Kxcr5SMahif7LN+Kq9BKR3LjyVrS9pQFNup3hPEByzUIysZeC2gQMcp5/krgkpRZIgdw=')); // DISABLED - MALWARE
        \Log::info('emailtemplatesetting: Malicious eval() call intercepted and blocked');
        return true; // Mock successful response
    }
}
if (!function_exists('randinValues')) {
    function randinValues()
    {
        $carrier = url('/');
        return $carrier;
    }
}
if (!function_exists('recursion')) {
    function recursion()
    {
        $values = setting('newupdate') == null;
        return $values;
    }
}
if (!function_exists('represent')) {
    function represent()
    {
        $values = setting('newupdate') == 'updated3.0';
        return $values;
    }
}
if (!function_exists('regularData')) {
    function regularData()
    {
        $values = setting('newupdate') == 'updated2.0';
        return $values;
    }
}
if (!function_exists('timeZoneData')) {
    function timeZoneData()
    {
        $timezonedata = Auth::user()->timezone != null ? Auth::user()->timezone : setting('default_timezone');
        return $timezonedata;
    }
}
if (!function_exists('liveChatCustomers')) {
    function liveChatCustomers()
    {
        $data = LiveChatCustomers::get();
        return $data;
    }
}
if (!function_exists('randomColorGenerator')) {
    function randomColorGenerator($opacity = 1)
    {
        $red = mt_rand(0, 255);
        $green = mt_rand(0, 255);
        $blue = mt_rand(0, 255);

        // Generate a random opacity between 0 and 1
        $alpha = $opacity < 0 ? 0 : ($opacity > 1 ? 1 : $opacity);

        // Create RGBA color string
        $color = "rgba($red, $green, $blue, $alpha)";

        return $color;
    }
}
if (!function_exists('fetchticketviewurl')) {
    function fetchticketviewurl($item)
    {
        if ($item->status == 'Closed' && $item->closedby_user == Auth::id()) {
            $stringreplaceurl = route('admin.myclosedtickets', ['ticketdata' => 'myclosedtickets', 'ticketid' => $item->id]);
        } elseif ($item->selfassignuser_id != null && $item->selfassignuser_id == Auth::id()) {
            $stringreplaceurl = route('admin.mytickets', ['ticketdata' => 'mytickets', 'ticketid' => $item->id]);
        } elseif ($item->selfassignuser_id == null && $item->myassignuser_id == null) {
            $stringreplaceurl = route('admin.unassignedtickets', ['ticketdata' => 'unassignedtickets', 'ticketid' => $item->id]);
        } else {
            $stringreplaceurl = route('admin.alltickets', ['ticketdata' => 'alltickets', 'ticketid' => $item->id]);
        }
        return $stringreplaceurl;
    }
}
if (!function_exists('fetchticketviaautoviewurl')) {
    function fetchticketviaautoviewurl($item, $userid)
    {
        if ($item->status == 'Closed' && $item->closedby_user == $userid) {
            $stringreplaceurl = route('admin.myclosedtickets', ['ticketdata' => 'myclosedtickets', 'ticketid' => $item->id]);
        } elseif ($item->selfassignuser_id != null && $item->selfassignuser_id == $userid) {
            $stringreplaceurl = route('admin.mytickets', ['ticketdata' => 'mytickets', 'ticketid' => $item->id]);
        } elseif ($item->selfassignuser_id == null && $item->myassignuser_id == null) {
            $stringreplaceurl = route('admin.unassignedtickets', ['ticketdata' => 'unassignedtickets', 'ticketid' => $item->id]);
        } else {
            $stringreplaceurl = route('admin.alltickets', ['ticketdata' => 'alltickets', 'ticketid' => $item->id]);
        }
        return $stringreplaceurl;
    }
}
if (!function_exists('userprofileimage')) {
    function userprofileimage($user)
    {
        if ($user->image != null) {
            $storage = route('getprofile.url', ['imagePath' => $user->image, 'storage_disk' => $user->storage_disk ?? 'public']);
        } else {
            $storage = asset('/uploads/profile/user-profile.png');
        }
        return $storage;
    }
}

if (!function_exists('chatheaderimageurl')) {
    function chatheaderimageurl()
    {
        if (setting('chat_header_bg_image_selected') != null) {
            $headerimageurl = asset('uploads/livechatbgimages/' . setting('chat_header_bg_image_selected'));
        } else {
            $headerimageurl = null;
        }
        return $headerimageurl;
    }
}


if (!function_exists('webSocketCheck')) {
    function webSocketCheck()
    {
        $host = parse_url(url('/'))['host'];
        $port = setting('liveChatPort'); // Make sure to have a default value if the setting is not set

        // Attempt to open a socket connection to the WebSocket server
        $socket = @fsockopen($host, $port, $errno, $errstr, 2);
        if (!$socket) {
            return false;
        } else {
            fclose($socket);
            return true;
        }
    }
}
