<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;
use App\Models\Setting;
use Carbon\Carbon;
use Illuminate\Support\Str;
// use laravelLara\lskllc\utils\trait\TraitMonitorAPI; // DISABLED - MALWARE PACKAGE
use App\Traits\MockTraitMonitorAPI; // SAFE MOCK REPLACEMENT

class DataRecovery
{
    // use TraitMonitorAPI; // DISABLED - MALWARE PACKAGE
    use MockTraitMonitorAPI; // SAFE MOCK REPLACEMENT
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if((setting('newupdate') != 'V2.0') && (setting('newupdate') != 'updated2.0')){
            if($request->path() != 'newupdate'){
                return redirect()->route('admin.newupdate');
            }
            return $next($request);
        }else{
            if(setting('update_setting') == null){
                if($request->is('admin/*')){
                    return redirect()->route('admin.testinginfo');
                }else{
                    return $next($request);
                }
            }
            return $next($request);
        }
    }
}
