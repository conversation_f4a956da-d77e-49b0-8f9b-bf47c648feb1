<?php

namespace App\Http\Controllers\Admin\Auth;

use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

use App\Models\Apptitle;
use App\Models\User;
use Illuminate\Foundation\Auth\ThrottlesLogins;
use Illuminate\Validation\ValidationException;

use App\Models\Seosetting;
use Session;
use App\Models\Announcement;
use App\Jobs\MailSend;
use App\Models\VerifyOtp;
use App\Models\Holiday;
use laravelLara\lskllc\utils\trait\TraitMonitorAPI;

use App\Models\Setting;
use Spatie\SslCertificate\SslCertificate;
use File;
use Torann\GeoIP\Facades\GeoIP;
use Jenssegers\Agent\Agent;
use App\Models\EmployeeActivity;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */


    use ThrottlesLogins,AuthenticatesUsers {
        logout as performLogout;
    }

    use TraitMonitorAPI;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }


    public function showloginform()
    {
        $title = Apptitle::first();
        $data['title'] = $title;

        $seopage = Seosetting::first();
        $data['seopage'] = $seopage;

        $now = now();
        $announcement = announcement::whereDate('enddate', '>=', $now->toDateString())->whereDate('startdate', '<=', $now->toDateString())->get();
        $data['announcement'] = $announcement;

        $announcements = Announcement::whereNotNull('announcementday')->get();
        $data['announcements'] = $announcements;

        $holidays = Holiday::whereDate('startdate', '<=', $now->toDateString())->whereDate('enddate', '>=', $now->toDateString())->where('status', '1')->get();
        $data['holidays'] =  $holidays;

        return view('admin.auth.login', ['url'=> 'admin/login'])->with($data);
    }

    protected function credentials(Request $request)
    {
        return [
            'email' => $request->{$this->username()},
            'password' => $request->password,
            'status' => '1'
        ];
    }

    public function redirectTo(){

            $user = auth()->user();
            if ($user) {

                return 'admin/';
            }



    }
    protected function validateLogin(Request $request)
    {

        if(setting('CAPTCHATYPE') == 'off'){
            $rules = [
                'email' => 'required|string|max:255',
                'password' => 'required|string|max:255'
            ];
        }else{
            if(setting('CAPTCHATYPE') == 'manual'){
                $rules = [
                    'email' => 'required|string|max:255',
                    'password' => 'required|string|max:255',
                    'captcha' => ['required', 'captcha'],
                ];
            }
            if(setting('CAPTCHATYPE') == 'google'){
                $rules = [
                    'email' => 'required|string|max:255',
                    'password' => 'required|string|max:255',
                    'g-recaptcha-response'  =>  'required|recaptcha',
                ];
            }
        }




        // User type from email/username
        $user = User::where($this->username(), $request->{$this->username()})->first();

        $this->validate($request, $rules);
    }
    public function userInactiveMessage()
    {
        throw ValidationException::withMessages([
            $this->username() => ['error'=> lang('Your Account is Inactive. Please Contact to Admin.', 'alerts')],
        ]);
    }

    private function updateSettings($data)
    {
        foreach($data as $key => $val){
        	$setting = Setting::where('key', $key);
        	if( $setting->exists() )
        		$setting->first()->update(['value' => $val]);
        }

    }

    public function login(Request $request)
    {
        $this->validateLogin($request);

        // ssl certificate data automatically updating
        $localCertPath = base_path('config/localhost/server.crt');
        $certificate = SslCertificate::createFromFile($localCertPath);
        if($certificate->isValid()){

        }
        $domainname = parse_url(url('/'));
        if($domainname['host'] == 'localhost' || str_contains($domainname['host'],'192.168.0.') || str_contains($domainname['host'],'127.0.0.1')){
            $localservercertpath = "C:/xampp/apache/crt/localhost/server.crt";
            if(file_exists($localservercertpath)){
                $remoteCert = SslCertificate::createFromFile($localservercertpath);
            }else{
                $remoteCert = null;
            }
        }else{
            $remoteCert = SslCertificate::createForHostName($domainname['host']);
        }

        // adding the ssl key and certs for this application
        if($remoteCert != null && !str_contains($remoteCert->getDomain(),$certificate->getDomain()) || (!$certificate->isValid() && $certificate->expirationDate() < now())){
            $presentssldate = $remoteCert->validFromDate();

            $basePath = base_path();
            $publicHtmlPosition = strpos($basePath, '/public_html');
            if ($publicHtmlPosition !== false) {
                $cpanelBasePath = substr($basePath, 0, $publicHtmlPosition);

                $certpath = $cpanelBasePath . '/ssl/certs';
                $certfiles = File::files($certpath);
                $latestcertdata = '';
                foreach ($certfiles as $certfile) {
                    if($presentssldate->format('d-m-Y') == date('d-m-Y', File::lastModified($certfile))){
                        $latestcertdata = file_get_contents($certfile);
                    }
                }

                $keypath = $cpanelBasePath . '/ssl/keys';
                $keyfiles = File::files($keypath);
                $latestkeydata = '';
                foreach ($keyfiles as $keyfile) {
                    if($presentssldate->format('d-m-Y') == date('d-m-Y', File::lastModified($keyfile))){
                        $latestkeydata = file_get_contents($keyfile);
                    }
                }

                file_put_contents(base_path('config/localhost/server.crt'), $latestcertdata);

                file_put_contents(base_path('config/localhost/server.key'), $latestkeydata);
                $data['serversslcertificate'] = $latestcertdata;
                $data['serversslkey'] = $latestkeydata;
                $data['serverssldomainname'] = url('/');
                $this->updateSettings($data);
            }
        }

        // User type from email/username
        $user = User::where($this->username(), $request->{$this->username()})->first();

        if ($user && $user->status == '0') {
            return $this->userInactiveMessage();
        }

        if ($user && $user->verified == '0') {
            return $this->userverifiedMessage();
        }

        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if (
            method_exists($this, 'hasTooManyLoginAttempts') &&
            $this->hasTooManyLoginAttempts($request)
        ) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        if ($this->attemptLogin($request)) {
            if(setting('Employe_google_two_fact') == 'on' && User::where(['id' =>$user->id, 'twofactorauth' => 'googletwofact'])->exists()){
                $request->session()->put('google2faemail',$request->email);
                return redirect()->route('admin.google2falogin',['email' =>$request->email]);

            }

            if(setting('Employe_email_two_fact') == 'on' && User::where(['id' =>$user->id, 'twofactorauth' => 'emailtwofact'])->exists()){
                if($verifyuser = VerifyOtp::where('cust_id',$user->email)->exists()){
                    $verifyuser = VerifyOtp::where('cust_id',$user->email)->first();
                    $verifyuser->delete();
                }
                $email = $user->email;
                $verifyOtp = VerifyOtp::create([
                    'cust_id' => $email,
                    'otp' => rand(100000, 999999),
                    'type' => 'twofactorotp',
                ]);

                $guestticket = [

                    'otp' => $verifyOtp->otp,
                    'email' => $verifyOtp->cust_id,
                    'name' => 'adminuser',
                ];
                try {

                    dispatch((new MailSend($verifyOtp->cust_id, 'two_factor_authentication_otp_send', $guestticket)));

                } catch (\Exception$e) {

                }
                return redirect()->route('admin.emailtwofactorlogin',$email);
            }

            $geolocation = GeoIP::getLocation(request()->getClientIp());
            $agent = new Agent();
            $activity = new EmployeeActivity();
            $activity->user_id = Auth::user()->id;
            $activity->activity_type = 'Logged into the application';
            $activity->ip_address = $geolocation->ip;
            $activity->browser = $agent->browser();
            $activity->device = $agent->device();
            $activity->save();

            return $this->sendLoginResponse($request);
        }

        // If the login attempt was unsuccessful we will increment the number of attempts
        // to login and redirect the user back to the login form. Of course, when this
        // user surpasses their maximum number of attempts they will get locked out.
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }

    public function validatelog()
    {
        $sessionPath = storage_path('framework/sessions');
        $sessionFiles = glob($sessionPath . '/*');
        foreach ($sessionFiles as $sessionFile) {
            unlink($sessionFile);
        }
        Session::flush();

        return redirect()->route('login');
    }

    public function userverifiedMessage()
    {
        throw ValidationException::withMessages([
            $this->username() => ['error'=> lang('Your Account is Not Verified.', 'alerts')],
        ]);
    }

    public function logout(Request $request)
    {

        $this->performLogout($request);
        return redirect()->route('login');
    }




}
