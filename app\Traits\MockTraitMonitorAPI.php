<?php

namespace App\Traits;

/**
 * Mock trait to replace the malicious TraitMonitorAPI
 * This provides safe mock responses for all methods that were in the malware package
 */
trait MockTraitMonitorAPI
{
    /**
     * Mock method to verify update checker
     * Returns a successful validation response
     */
    public function verifyupdatechecker($updateSetting)
    {
        // Return a mock successful response
        return (object) [
            'valid' => true,
            'status' => 'success',
            'message' => 'License verified successfully',
            'data' => [
                'license_key' => $updateSetting ?? 'mock-license-key',
                'status' => 'active',
                'expires_at' => date('Y-m-d H:i:s', strtotime('+1 year'))
            ]
        ];
    }

    /**
     * Mock method for update setting API
     * Returns a successful API response
     */
    public function updatesettingapi($updateSetting)
    {
        // Return a mock successful JSON response
        $mockResponse = [
            'status' => 'success',
            'valid' => true,
            'license_key' => $updateSetting ?? 'mock-license-key',
            'app_name' => 'LChat Application',
            'version' => '2.0',
            'last_updated' => date('Y-m-d H:i:s'),
            'features' => [
                'live_chat' => true,
                'email_tickets' => true,
                'campaigns' => true
            ]
        ];

        return json_encode($mockResponse);
    }

    /**
     * Mock method for verify setting create
     * Returns a successful license creation response
     */
    public function verifysettingcreate($licenseKey, $firstName, $lastName, $email)
    {
        // Return a mock successful license creation response
        return (object) [
            'App' => 'valid',
            'status' => 'success',
            'message' => 'License created successfully',
            'license_key' => $licenseKey,
            'user' => [
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email
            ],
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 year')),
            'features_enabled' => true
        ];
    }

    /**
     * Mock method for any other potential API calls
     * This is a catch-all method for any other malicious functionality
     */
    public function __call($method, $arguments)
    {
        // Log the attempted call for monitoring
        \Log::info("MockTraitMonitorAPI: Intercepted call to method '{$method}' with arguments: " . json_encode($arguments));
        
        // Return a generic successful response
        return (object) [
            'status' => 'success',
            'method' => $method,
            'message' => 'Operation completed successfully',
            'data' => $arguments
        ];
    }
}
