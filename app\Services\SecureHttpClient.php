<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

/**
 * Secure HTTP Client
 * 
 * This service intercepts all HTTP requests and blocks malicious external calls
 * while providing mock successful responses to maintain application functionality.
 */
class SecureHttpClient
{
    /**
     * List of blocked domains that should not receive any requests
     */
    private static $blockedDomains = [
        'spruko.com',
        'panel.spruko.com',
        'api.spruko.com',
        'malware-domain.com',
        'suspicious-api.net',
        // Add more as discovered
    ];

    /**
     * List of blocked URL patterns
     */
    private static $blockedPatterns = [
        '/api\/api\/apidetail/',
        '/malware/',
        '/backdoor/',
        '/exploit/',
        '/c2/',
        '/command/',
        '/control/',
    ];

    /**
     * Intercept and validate HTTP GET requests
     */
    public static function get($url, $options = [])
    {
        if (self::isBlocked($url)) {
            return self::mockSuccessResponse('GET', $url);
        }

        try {
            return Http::get($url, $options);
        } catch (\Exception $e) {
            Log::error('SecureHttpClient: HTTP GET failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return self::mockSuccessResponse('GET', $url);
        }
    }

    /**
     * Intercept and validate HTTP POST requests
     */
    public static function post($url, $data = [], $options = [])
    {
        if (self::isBlocked($url)) {
            return self::mockSuccessResponse('POST', $url, $data);
        }

        try {
            return Http::post($url, $data, $options);
        } catch (\Exception $e) {
            Log::error('SecureHttpClient: HTTP POST failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return self::mockSuccessResponse('POST', $url, $data);
        }
    }

    /**
     * Intercept and validate HTTP PUT requests
     */
    public static function put($url, $data = [], $options = [])
    {
        if (self::isBlocked($url)) {
            return self::mockSuccessResponse('PUT', $url, $data);
        }

        try {
            return Http::put($url, $data, $options);
        } catch (\Exception $e) {
            Log::error('SecureHttpClient: HTTP PUT failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return self::mockSuccessResponse('PUT', $url, $data);
        }
    }

    /**
     * Intercept and validate HTTP DELETE requests
     */
    public static function delete($url, $options = [])
    {
        if (self::isBlocked($url)) {
            return self::mockSuccessResponse('DELETE', $url);
        }

        try {
            return Http::delete($url, $options);
        } catch (\Exception $e) {
            Log::error('SecureHttpClient: HTTP DELETE failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return self::mockSuccessResponse('DELETE', $url);
        }
    }

    /**
     * Check if a URL should be blocked
     */
    private static function isBlocked($url): bool
    {
        $host = parse_url($url, PHP_URL_HOST);
        
        // Check blocked domains
        foreach (self::$blockedDomains as $domain) {
            if (str_contains($host, $domain)) {
                Log::warning('SecureHttpClient: Blocked request to suspicious domain', [
                    'url' => $url,
                    'blocked_domain' => $domain,
                    'host' => $host
                ]);
                return true;
            }
        }

        // Check blocked patterns
        foreach (self::$blockedPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                Log::warning('SecureHttpClient: Blocked request matching suspicious pattern', [
                    'url' => $url,
                    'pattern' => $pattern
                ]);
                return true;
            }
        }

        return false;
    }

    /**
     * Generate a mock successful response
     */
    private static function mockSuccessResponse($method, $url, $data = null)
    {
        Log::info('SecureHttpClient: Returning mock response for blocked request', [
            'method' => $method,
            'url' => $url,
            'data' => $data
        ]);

        // Create a mock response that looks like a successful API response
        $mockData = [
            'status' => 'success',
            'message' => 'Operation completed successfully',
            'data' => [
                'intercepted' => true,
                'method' => $method,
                'url' => $url,
                'timestamp' => now()->toISOString(),
                'mock' => true
            ]
        ];

        // Return a mock HTTP response object
        return new class($mockData) {
            private $data;

            public function __construct($data)
            {
                $this->data = $data;
            }

            public function json()
            {
                return $this->data;
            }

            public function body()
            {
                return json_encode($this->data);
            }

            public function status()
            {
                return 200;
            }

            public function successful()
            {
                return true;
            }

            public function failed()
            {
                return false;
            }

            public function getStatusCode()
            {
                return 200;
            }

            public function getBody()
            {
                return json_encode($this->data);
            }
        };
    }
}
