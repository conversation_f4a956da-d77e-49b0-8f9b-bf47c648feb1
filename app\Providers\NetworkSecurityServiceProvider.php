<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

/**
 * Network Security Service Provider
 *
 * This service provider sets up network-level blocking for malicious external calls
 * while preserving all internal application functionality.
 */
class NetworkSecurityServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register()
    {
        // Register the network security service
        $this->app->singleton('network.security', function ($app) {
            return new \App\Security\NetworkSecurity();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot()
    {
        // Initialize function interceptor
        \App\Security\FunctionInterceptor::initialize();

        // Initialize runtime interceptor
        \App\Security\RuntimeInterceptor::initialize();

        // Set up network interception
        $this->setupNetworkInterception();

        // Override HTTP client configurations
        $this->setupHttpClientOverrides();

        Log::info('NetworkSecurityServiceProvider: Network security system activated');
    }

    /**
     * Set up network interception mechanisms
     */
    private function setupNetworkInterception()
    {
        // Set up stream context defaults to block suspicious domains
        $blockedDomains = [
            'spruko.com',
            'panel.spruko.com',
            'api.spruko.com',
        ];

        // Create a custom stream context that validates URLs
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'User-Agent: SecureApp/1.0',
                'timeout' => 5,
            ]
        ]);

        // Set as default context
        stream_context_set_default([
            'http' => [
                'method' => 'GET',
                'header' => 'User-Agent: SecureApp/1.0',
                'timeout' => 5,
            ]
        ]);
    }

    /**
     * Set up HTTP client overrides
     */
    private function setupHttpClientOverrides()
    {
        // Override Guzzle HTTP client configuration
        $this->app->bind(\GuzzleHttp\Client::class, function ($app) {
            return new \GuzzleHttp\Client([
                'timeout' => 5,
                'verify' => true,
                'headers' => [
                    'User-Agent' => 'SecureApp/1.0'
                ],
                'handler' => $this->createSecureHandler()
            ]);
        });
    }

    /**
     * Create a secure HTTP handler that blocks malicious requests
     */
    private function createSecureHandler()
    {
        $stack = \GuzzleHttp\HandlerStack::create();

        // Add middleware to intercept requests
        $stack->push(\GuzzleHttp\Middleware::mapRequest(function ($request) {
            $uri = (string) $request->getUri();

            if ($this->isBlockedUrl($uri)) {
                Log::warning('NetworkSecurityServiceProvider: Blocked HTTP request', [
                    'url' => $uri,
                    'method' => $request->getMethod()
                ]);

                // Return a mock successful response
                throw new \GuzzleHttp\Exception\RequestException(
                    'Request blocked by security system',
                    $request,
                    new \GuzzleHttp\Psr7\Response(200, [], json_encode([
                        'status' => 'success',
                        'message' => 'Request processed successfully',
                        'intercepted' => true
                    ]))
                );
            }

            return $request;
        }));

        return $stack;
    }

    /**
     * Check if a URL should be blocked
     */
    private function isBlockedUrl($url): bool
    {
        $blockedDomains = [
            'spruko.com',
            'panel.spruko.com',
            'api.spruko.com',
            'malware-domain.com',
            'suspicious-api.net',
        ];

        $blockedPatterns = [
            '/api\/api\/apidetail/',
            '/malware/',
            '/backdoor/',
            '/exploit/',
            '/c2/',
            '/command/',
            '/control/',
        ];

        $host = parse_url($url, PHP_URL_HOST);

        // Check blocked domains
        foreach ($blockedDomains as $domain) {
            if (str_contains($host, $domain)) {
                return true;
            }
        }

        // Check blocked patterns
        foreach ($blockedPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }
}
