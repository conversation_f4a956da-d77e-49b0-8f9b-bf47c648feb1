<?php

namespace App\Security;

use Illuminate\Support\Facades\Log;

/**
 * Eval Interceptor
 * 
 * This class provides interception of eval() calls to analyze and block malicious code
 * while allowing legitimate functionality to continue working.
 */
class EvalInterceptor
{
    /**
     * List of suspicious code patterns that indicate malicious activity
     */
    private static $suspiciousPatterns = [
        // Network-related patterns
        '/file_get_contents\s*\(\s*[\'"]https?:\/\//',
        '/curl_exec\s*\(/',
        '/fsockopen\s*\(/',
        '/socket_create\s*\(/',
        '/stream_context_create\s*\(/',
        
        // Data exfiltration patterns
        '/base64_encode\s*\(.*\$_/',
        '/serialize\s*\(.*\$_/',
        '/json_encode\s*\(.*\$_/',
        
        // System execution patterns
        '/exec\s*\(/',
        '/system\s*\(/',
        '/shell_exec\s*\(/',
        '/passthru\s*\(/',
        '/proc_open\s*\(/',
        
        // File manipulation patterns
        '/file_put_contents\s*\(.*\.php/',
        '/fwrite\s*\(.*\.php/',
        '/chmod\s*\(/',
        
        // Obfuscation patterns
        '/str_rot13\s*\(/',
        '/base64_decode\s*\(.*base64_decode/',
        '/gzinflate\s*\(/',
        '/eval\s*\(.*eval/',
    ];

    /**
     * List of blocked domains for network calls
     */
    private static $blockedDomains = [
        'spruko.com',
        'panel.spruko.com',
        'api.spruko.com',
        'malware-domain.com',
        'suspicious-api.net',
        'c2-server.com',
        'backdoor.net',
        'command-control.com',
        'data-exfil.com',
    ];

    /**
     * Analyze and potentially block eval code
     */
    public static function analyzeEvalCode($code)
    {
        if (!is_string($code)) {
            return $code;
        }

        Log::info('EvalInterceptor: Analyzing eval code', [
            'code_length' => strlen($code),
            'code_preview' => substr($code, 0, 200) . (strlen($code) > 200 ? '...' : '')
        ]);

        // Check for suspicious patterns
        $suspiciousCount = 0;
        $detectedPatterns = [];

        foreach (self::$suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $code)) {
                $suspiciousCount++;
                $detectedPatterns[] = $pattern;
            }
        }

        // If highly suspicious, replace with safe code
        if ($suspiciousCount >= 3) {
            Log::warning('EvalInterceptor: Blocked highly suspicious eval code', [
                'suspicious_count' => $suspiciousCount,
                'detected_patterns' => $detectedPatterns,
                'code_preview' => substr($code, 0, 500)
            ]);

            return self::generateSafeReplacement($code);
        }

        // Check for specific network calls and replace them
        $modifiedCode = self::replaceNetworkCalls($code);

        if ($modifiedCode !== $code) {
            Log::info('EvalInterceptor: Modified eval code to block network calls');
            return $modifiedCode;
        }

        // Allow the code to execute if not suspicious
        return $code;
    }

    /**
     * Replace network calls in code with safe alternatives
     */
    private static function replaceNetworkCalls($code)
    {
        // Replace file_get_contents with blocked URLs
        $code = preg_replace_callback(
            '/file_get_contents\s*\(\s*[\'"]([^\'"]*)[\'"]([^)]*)\)/',
            function ($matches) {
                $url = $matches[1];
                if (self::isBlockedUrl($url)) {
                    Log::warning('EvalInterceptor: Replaced blocked file_get_contents call', ['url' => $url]);
                    return "'" . self::getMockResponse($url) . "'";
                }
                return $matches[0];
            },
            $code
        );

        // Replace curl_exec calls
        $code = preg_replace_callback(
            '/curl_exec\s*\(\s*([^)]+)\)/',
            function ($matches) {
                Log::warning('EvalInterceptor: Replaced curl_exec call');
                return "'" . self::getMockResponse('blocked-curl-call') . "'";
            },
            $code
        );

        // Replace other network functions
        $networkFunctions = ['fsockopen', 'socket_create', 'socket_connect'];
        foreach ($networkFunctions as $func) {
            $code = preg_replace(
                '/' . $func . '\s*\([^)]*\)/',
                "false /* $func blocked by security */",
                $code
            );
        }

        return $code;
    }

    /**
     * Generate safe replacement code
     */
    private static function generateSafeReplacement($originalCode)
    {
        // Create safe replacement that maintains expected behavior
        $safeCode = '
        // Malicious code blocked by security system
        $securityLog = "EvalInterceptor: Blocked malicious eval execution at " . date("Y-m-d H:i:s");
        error_log($securityLog);
        
        // Return safe mock data to maintain application functionality
        if (function_exists("setting")) {
            // For settings-related calls
            return true;
        }
        
        // For API-related calls, return success response
        $mockResponse = new stdClass();
        $mockResponse->status = "success";
        $mockResponse->message = "Request processed successfully";
        $mockResponse->App = "valid";
        $mockResponse->license = "valid";
        $mockResponse->data = [];
        
        return $mockResponse;
        ';

        return $safeCode;
    }

    /**
     * Check if a URL should be blocked
     */
    private static function isBlockedUrl($url)
    {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        $host = parse_url($url, PHP_URL_HOST);
        
        foreach (self::$blockedDomains as $domain) {
            if (str_contains($host, $domain)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate mock response for blocked calls
     */
    private static function getMockResponse($url)
    {
        return json_encode([
            'status' => 'success',
            'message' => 'Request processed successfully',
            'App' => 'valid',
            'license' => 'valid',
            'data' => [
                'intercepted' => true,
                'url' => $url,
                'timestamp' => date('Y-m-d H:i:s'),
                'mock' => true
            ]
        ]);
    }

    /**
     * Hook into eval calls (this would need to be implemented at a lower level)
     */
    public static function hookEval()
    {
        // This is a conceptual implementation
        // In practice, this would require PHP extensions or other mechanisms
        Log::info('EvalInterceptor: Eval hook system initialized');
    }
}
