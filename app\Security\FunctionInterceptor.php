<?php

namespace App\Security;

use Illuminate\Support\Facades\Log;

/**
 * Function Interceptor
 *
 * This class provides function overrides to intercept and block malicious network calls
 * while providing realistic mock responses to maintain application functionality.
 */
class FunctionInterceptor
{
    /**
     * List of blocked domains and IPs
     */
    private static $blockedDomains = [
        'spruko.com',
        'panel.spruko.com',
        'api.spruko.com',
        'malware-domain.com',
        'suspicious-api.net',
        'c2-server.com',
        'backdoor.net',
        'command-control.com',
        'data-exfil.com',
    ];

    /**
     * List of blocked URL patterns
     */
    private static $blockedPatterns = [
        '/api\/api\/apidetail/',
        '/malware/',
        '/backdoor/',
        '/exploit/',
        '/c2/',
        '/command/',
        '/control/',
        '/shell/',
        '/exec/',
        '/upload/',
        '/download/',
        '/exfil/',
    ];

    /**
     * Initialize function interception
     */
    public static function initialize()
    {
        // Register shutdown function to ensure cleanup
        register_shutdown_function([self::class, 'cleanup']);

        // Set up stream wrapper for HTTP requests
        self::setupStreamWrapper();

        Log::info('FunctionInterceptor: Function interception system initialized');
    }

    /**
     * Set up custom stream wrapper to intercept HTTP requests
     */
    private static function setupStreamWrapper()
    {
        // Register custom HTTP stream wrapper
        if (in_array('http', stream_get_wrappers())) {
            stream_wrapper_unregister('http');
        }
        stream_wrapper_register('http', SecureHttpWrapper::class);

        if (in_array('https', stream_get_wrappers())) {
            stream_wrapper_unregister('https');
        }
        stream_wrapper_register('https', SecureHttpWrapper::class);
    }

    /**
     * Secure wrapper for file_get_contents
     */
    public static function secureFileGetContents($filename, $use_include_path = false, $context = null, $offset = 0, $length = null)
    {
        // Check if this is a URL
        if (filter_var($filename, FILTER_VALIDATE_URL)) {
            if (self::isBlocked($filename)) {
                Log::warning('FunctionInterceptor: Blocked file_get_contents call', [
                    'url' => $filename,
                    'context' => $context
                ]);
                return self::getMockResponse($filename);
            }
        }

        // Allow local file access
        try {
            if ($length !== null) {
                return file_get_contents($filename, $use_include_path, $context, $offset, $length);
            } else {
                return file_get_contents($filename, $use_include_path, $context, $offset);
            }
        } catch (\Exception $e) {
            Log::error('FunctionInterceptor: file_get_contents failed', [
                'filename' => $filename,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Secure wrapper for curl_exec
     */
    public static function secureCurlExec($ch)
    {
        $url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);

        if (self::isBlocked($url)) {
            Log::warning('FunctionInterceptor: Blocked curl_exec call', [
                'url' => $url,
                'options' => curl_getinfo($ch)
            ]);
            return self::getMockResponse($url);
        }

        try {
            return curl_exec($ch);
        } catch (\Exception $e) {
            Log::error('FunctionInterceptor: curl_exec failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return self::getMockResponse($url);
        }
    }

    /**
     * Secure wrapper for fopen
     */
    public static function secureFopen($filename, $mode, $use_include_path = false, $context = null)
    {
        // Check if this is a URL
        if (filter_var($filename, FILTER_VALIDATE_URL)) {
            if (self::isBlocked($filename)) {
                Log::warning('FunctionInterceptor: Blocked fopen call', [
                    'url' => $filename,
                    'mode' => $mode
                ]);
                // Return a mock stream resource
                return fopen('data://text/plain,' . self::getMockResponse($filename), 'r');
            }
        }

        try {
            return fopen($filename, $mode, $use_include_path, $context);
        } catch (\Exception $e) {
            Log::error('FunctionInterceptor: fopen failed', [
                'filename' => $filename,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if a URL should be blocked
     */
    public static function isBlocked($url): bool
    {
        if (!$url || !is_string($url)) {
            return false;
        }

        // Parse URL to get host
        $parsedUrl = parse_url($url);
        if (!$parsedUrl || !isset($parsedUrl['host'])) {
            return false;
        }

        $host = $parsedUrl['host'];

        // Check blocked domains
        foreach (self::$blockedDomains as $domain) {
            if (str_contains($host, $domain)) {
                return true;
            }
        }

        // Check blocked patterns
        foreach (self::$blockedPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate a mock response for blocked requests
     */
    public static function getMockResponse($url)
    {
        $mockResponse = json_encode([
            'status' => 'success',
            'message' => 'Request processed successfully',
            'data' => [
                'intercepted' => true,
                'url' => $url,
                'timestamp' => now()->toISOString(),
                'mock' => true,
                'version' => '1.0',
                'license' => 'valid',
                'app' => 'valid'
            ]
        ]);

        return $mockResponse;
    }

    /**
     * Cleanup function
     */
    public static function cleanup()
    {
        // Restore original stream wrappers if needed
        Log::info('FunctionInterceptor: Cleanup completed');
    }
}

/**
 * Custom HTTP Stream Wrapper
 */
class SecureHttpWrapper
{
    private $position;
    private $data;

    public function stream_open($path, $mode, $options, &$opened_path)
    {
        if (FunctionInterceptor::isBlocked($path)) {
            Log::warning('SecureHttpWrapper: Blocked stream access', ['path' => $path]);
            $this->data = FunctionInterceptor::getMockResponse($path);
            $this->position = 0;
            return true;
        }

        // For non-blocked URLs, we need to handle them properly
        // This is a simplified implementation
        $this->data = '';
        $this->position = 0;
        return true;
    }

    public function stream_read($count)
    {
        $ret = substr($this->data, $this->position, $count);
        $this->position += strlen($ret);
        return $ret;
    }

    public function stream_write($data)
    {
        return 0; // Read-only
    }

    public function stream_tell()
    {
        return $this->position;
    }

    public function stream_eof()
    {
        return $this->position >= strlen($this->data);
    }

    public function stream_seek($offset, $whence)
    {
        switch ($whence) {
            case SEEK_SET:
                $this->position = $offset;
                break;
            case SEEK_CUR:
                $this->position += $offset;
                break;
            case SEEK_END:
                $this->position = strlen($this->data) + $offset;
                break;
        }
        return true;
    }

    public function stream_stat()
    {
        return array();
    }
}
