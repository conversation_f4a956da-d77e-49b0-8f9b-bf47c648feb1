<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

/**
 * Mock service provider to replace the malicious OnboardingServiceProvider
 * This provides safe mock functionality without executing any malicious code
 */
class MockOnboardingServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Safe registration logic - no malicious code execution
        $this->app->singleton('mock.onboarding', function ($app) {
            return new class {
                public function status()
                {
                    return 'active';
                }
                
                public function version()
                {
                    return '2.0';
                }
                
                public function features()
                {
                    return [
                        'live_chat' => true,
                        'email_tickets' => true,
                        'campaigns' => true
                    ];
                }
            };
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Safe boot logic - no malicious code execution
        \Log::info('MockOnboardingServiceProvider: Safe service provider loaded successfully');
    }
}
