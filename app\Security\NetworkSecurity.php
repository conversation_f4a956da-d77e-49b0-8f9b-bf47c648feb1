<?php

namespace App\Security;

use Illuminate\Support\Facades\Log;

/**
 * Network Security Class
 * 
 * This class provides secure wrappers for network functions to prevent malicious external calls
 * while maintaining application functionality with mock responses.
 */
class NetworkSecurity
{
    /**
     * List of blocked domains
     */
    private static $blockedDomains = [
        'spruko.com',
        'panel.spruko.com',
        'api.spruko.com',
        'malware-domain.com',
        'suspicious-api.net',
        'c2-server.com',
        'backdoor.net',
    ];

    /**
     * List of blocked URL patterns
     */
    private static $blockedPatterns = [
        '/api\/api\/apidetail/',
        '/malware/',
        '/backdoor/',
        '/exploit/',
        '/c2/',
        '/command/',
        '/control/',
        '/shell/',
        '/exec/',
    ];

    /**
     * Secure wrapper for file_get_contents
     */
    public static function secureFileGetContents($url, $context = null)
    {
        if (self::isBlocked($url)) {
            Log::warning('NetworkSecurity: Blocked file_get_contents call', ['url' => $url]);
            return self::getMockResponse($url);
        }

        // Allow local file access
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return file_get_contents($url, false, $context);
        }

        try {
            return file_get_contents($url, false, $context);
        } catch (\Exception $e) {
            Log::error('NetworkSecurity: file_get_contents failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return self::getMockResponse($url);
        }
    }

    /**
     * Secure wrapper for curl_exec
     */
    public static function secureCurlExec($ch)
    {
        $url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        
        if (self::isBlocked($url)) {
            Log::warning('NetworkSecurity: Blocked curl_exec call', ['url' => $url]);
            return self::getMockResponse($url);
        }

        try {
            return curl_exec($ch);
        } catch (\Exception $e) {
            Log::error('NetworkSecurity: curl_exec failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return self::getMockResponse($url);
        }
    }

    /**
     * Check if a URL should be blocked
     */
    private static function isBlocked($url): bool
    {
        if (!$url || !is_string($url)) {
            return false;
        }

        // Parse URL to get host
        $parsedUrl = parse_url($url);
        if (!$parsedUrl || !isset($parsedUrl['host'])) {
            return false;
        }

        $host = $parsedUrl['host'];

        // Check blocked domains
        foreach (self::$blockedDomains as $domain) {
            if (str_contains($host, $domain)) {
                return true;
            }
        }

        // Check blocked patterns
        foreach (self::$blockedPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate a mock response for blocked requests
     */
    private static function getMockResponse($url)
    {
        $mockResponse = json_encode([
            'status' => 'success',
            'message' => 'Request processed successfully',
            'data' => [
                'intercepted' => true,
                'url' => $url,
                'timestamp' => now()->toISOString(),
                'mock' => true
            ]
        ]);

        return $mockResponse;
    }

    /**
     * Initialize network security by overriding global functions
     */
    public static function initialize()
    {
        // This would ideally override global functions, but PHP doesn't allow this easily
        // Instead, we'll need to modify the malicious code to use our secure wrappers
        Log::info('NetworkSecurity: Security system initialized');
    }
}
